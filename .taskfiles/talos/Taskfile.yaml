---
# yaml-language-server: $schema=https://taskfile.dev/schema.json
version: '3'

tasks:

  apply-node:
    desc: Apply <PERSON><PERSON> config to a node [IP=required] [MODE=auto]
    cmd: |
      bash {{.SCRIPTS_DIR}}/render-machine-config.sh {{.TALOS_DIR}}/{{.MACHINE_TYPE}}.yaml.j2 {{.TALOS_DIR}}/nodes/{{.IP}}.yaml.j2 \
        | talosctl --nodes {{.IP}} apply-config --mode {{.MODE}} --file /dev/stdin
    vars:
      MODE: '{{.MODE | default "auto"}}'
      MACHINE_TYPE:
        sh: talosctl --nodes {{.IP}} get machinetypes --output=jsonpath='{.spec}'
    requires:
      vars: [IP]
    preconditions:
      - talosctl --nodes {{.IP}} get machineconfig
      - test -f {{.SCRIPTS_DIR}}/render-machine-config.sh
      - which talosctl

  upgrade-node:
    desc: Upgrade Talos on a single node [IP=required] [MODE=powercycle]
    cmd: talosctl --nodes {{.IP}} upgrade --image="{{.FACTORY_IMAGE}}" --reboot-mode={{.MODE}} --timeout=10m
    vars:
      MODE: '{{.MODE | default "powercycle"}}'
      FACTORY_IMAGE:
        sh: minijinja-cli {{.TALOS_DIR}}/nodes/{{.IP}}.yaml.j2 | yq --exit-status '.machine.install.image'
    requires:
      vars: [IP]
    preconditions:
      - talosctl --nodes {{.IP}} get machineconfig
      - test -f {{.TALOS_DIR}}/nodes/{{.IP}}.yaml.j2
      - which minijinja-cli talosctl yq

  upgrade-k8s:
    desc: Upgrade Kubernetes across the whole cluster
    cmd: talosctl --nodes {{.NODE}} upgrade-k8s --to $KUBERNETES_VERSION
    vars:
      NODE:
        sh: talosctl config info --output json | jq --exit-status --raw-output '.endpoints[]' | shuf -n 1
    preconditions:
      - talosctl --nodes {{.NODE}} get machineconfig
      - which jq talosctl

  reboot-node:
    desc: Reboot Talos on a single node [IP=required] [MODE=powercycle]
    cmd: talosctl --nodes {{.IP}} reboot --mode={{.MODE}}
    vars:
      MODE: '{{.MODE | default "powercycle"}}'
    requires:
      vars: [IP]
    preconditions:
      - talosctl --nodes {{.IP}} get machineconfig
      - which talosctl

  shutdown-cluster:
    desc: Shutdown Talos across the whole cluster
    prompt: Shutdown the Talos cluster ... continue?
    cmd: talosctl shutdown --nodes {{.NODES}} --force
    vars:
      NODES:
        sh: talosctl config info --output json | jq --exit-status --join-output '[.nodes[]] | join(",")'
    preconditions:
      - talosctl --nodes {{.NODES}} get machineconfig
      - which jq talosctl

  reset-node:
    desc: Reset Talos on a single node [IP=required]
    prompt: Reset Talos node '{{.IP}}' ... continue?
    cmd: talosctl reset --nodes {{.IP}} --graceful=false
    requires:
      vars: [IP]
    preconditions:
      - talosctl --nodes {{.IP}} get machineconfig
      - which talosctl

  reset-cluster:
    desc: Reset Talos across the whole cluster
    prompt: Reset the Talos cluster ... continue?
    cmd: talosctl reset --nodes {{.NODES}} --graceful=false
    vars:
      NODES:
        sh: talosctl config info --output json | jq --exit-status --join-output '[.nodes[]] | join(",")'
    preconditions:
      - talosctl --nodes {{.NODES}} get machineconfig
      - which jq talosctl

  kubeconfig:
    desc: Generate the kubeconfig for a Talos cluster
    cmd: talosctl kubeconfig --nodes {{.NODE}} --force --force-context-name main {{.ROOT_DIR}}
    vars:
      NODE:
        sh: talosctl config info --output json | jq --exit-status --raw-output '.endpoints[]' | shuf -n 1
    preconditions:
      - talosctl --nodes {{.NODE}} get machineconfig
      - which jq talosctl

  deploy-vm:
    desc: Deploy Talos VM with custom ZVol paths [NAME=required] [MEMORY=4096] [VCPUS=2] [DISK_SIZE=250] [OPENEBS_SIZE=1024] [ROOK_SIZE=800] [MAC_ADDRESS=optional] [BOOT_ZVOL=optional] [OPENEBS_ZVOL=optional] [ROOK_ZVOL=optional] [SKIP_ZVOL_CREATE=false]
    cmd: |
      echo '{{.SCRIPTS_DIR}}/deploy-truenas-vm \
        -name "{{.NAME}}" \
        -memory {{.MEMORY}} \
        -vcpus {{.VCPUS}} \
        {{if not .BOOT_ZVOL}}-disk-size {{.DISK_SIZE}}{{end}} \
        {{if not .OPENEBS_ZVOL}}-openebs-size {{.OPENEBS_SIZE}}{{end}} \
        {{if not .ROOK_ZVOL}}-rook-size {{.ROOK_SIZE}}{{end}} \
        -truenas-host "op://Infrastructure/talosdeploy/TRUENAS_HOST" \
        -truenas-api-key "op://Infrastructure/talosdeploy/TRUENAS_API" \
        -spice-password "op://Infrastructure/talosdeploy/TRUENAS_SPICE_PASS" \
        -network-bridge "{{.NETWORK_BRIDGE}}" \
        {{if .MAC_ADDRESS}}-mac-address "{{.MAC_ADDRESS}}"{{end}} \
        {{if .BOOT_ZVOL}}-boot-zvol "{{.BOOT_ZVOL}}"{{end}} \
        {{if .OPENEBS_ZVOL}}-openebs-zvol "{{.OPENEBS_ZVOL}}"{{end}} \
        {{if .ROOK_ZVOL}}-rook-zvol "{{.ROOK_ZVOL}}"{{end}} \
        {{if .SKIP_ZVOL_CREATE}}-skip-zvol-create{{end}}' | op inject | bash
    vars:
      MEMORY: '{{.MEMORY | default "4096"}}'
      VCPUS: '{{.VCPUS | default "2"}}'
      DISK_SIZE: '{{.DISK_SIZE | default "250"}}'
      OPENEBS_SIZE: '{{.OPENEBS_SIZE | default "1024"}}'
      ROOK_SIZE: '{{.ROOK_SIZE | default "800"}}'
      NETWORK_BRIDGE: '{{.NETWORK_BRIDGE | default "br0"}}'
    requires:
      vars: [NAME]
    preconditions:
      - test -f {{.SCRIPTS_DIR}}/deploy-truenas-vm
      - which op

  list-vms:
    desc: List all VMs on TrueNAS Scale
    cmd: |
      echo '{{.SCRIPTS_DIR}}/manage-truenas-vm \
        -action list \
        -truenas-host "op://Infrastructure/talosdeploy/TRUENAS_HOST" \
        -truenas-api-key "op://Infrastructure/talosdeploy/TRUENAS_API"' | op inject | bash
    preconditions:
      - test -f {{.SCRIPTS_DIR}}/manage-truenas-vm
      - which op

  start-vm:
    desc: Start a VM on TrueNAS Scale [NAME=required]
    cmd: |
      echo '{{.SCRIPTS_DIR}}/manage-truenas-vm \
        -action start \
        -name "{{.NAME}}" \
        -truenas-host "op://Infrastructure/talosdeploy/TRUENAS_HOST" \
        -truenas-api-key "op://Infrastructure/talosdeploy/TRUENAS_API"' | op inject | bash
    requires:
      vars: [NAME]
    preconditions:
      - test -f {{.SCRIPTS_DIR}}/manage-truenas-vm
      - which op

  stop-vm:
    desc: Stop a VM on TrueNAS Scale [NAME=required]
    cmd: |
      echo '{{.SCRIPTS_DIR}}/manage-truenas-vm \
        -action stop \
        -name "{{.NAME}}" \
        -truenas-host "op://Infrastructure/talosdeploy/TRUENAS_HOST" \
        -truenas-api-key "op://Infrastructure/talosdeploy/TRUENAS_API"' | op inject | bash
    requires:
      vars: [NAME]
    preconditions:
      - test -f {{.SCRIPTS_DIR}}/manage-truenas-vm
      - which op

  info-vm:
    desc: Get detailed information about a VM on TrueNAS Scale [NAME=required]
    cmd: |
      echo '{{.SCRIPTS_DIR}}/manage-truenas-vm \
        -action info \
        -name "{{.NAME}}" \
        -truenas-host "op://Infrastructure/talosdeploy/TRUENAS_HOST" \
        -truenas-api-key "op://Infrastructure/talosdeploy/TRUENAS_API"' | op inject | bash
    requires:
      vars: [NAME]
    preconditions:
      - test -f {{.SCRIPTS_DIR}}/manage-truenas-vm
      - which op

  delete-vm:
    desc: Delete a VM and all associated ZVols on TrueNAS Scale [NAME=required]
    prompt: Delete VM '{{.NAME}}' and all its ZVols from TrueNAS ... continue?
    cmd: |
      echo '{{.SCRIPTS_DIR}}/manage-truenas-vm \
        -action delete \
        -name "{{.NAME}}" \
        -truenas-host "op://Infrastructure/talosdeploy/TRUENAS_HOST" \
        -truenas-api-key "op://Infrastructure/talosdeploy/TRUENAS_API"' | op inject | bash
    requires:
      vars: [NAME]
    preconditions:
      - test -f {{.SCRIPTS_DIR}}/manage-truenas-vm
      - which op

  deploy-vm-with-pattern:
    desc: Deploy VM with auto-generated ZVol paths (RECOMMENDED) [NAME=required] [POOL=flashstor] [MEMORY=4096] [VCPUS=2] [DISK_SIZE=250] [OPENEBS_SIZE=1024] [ROOK_SIZE=800] [MAC_ADDRESS=optional]
    cmd: |
      task talos:deploy-vm \
        NAME={{.NAME}} \
        MEMORY={{.MEMORY}} \
        VCPUS={{.VCPUS}} \
        DISK_SIZE={{.DISK_SIZE}} \
        OPENEBS_SIZE={{.OPENEBS_SIZE}} \
        ROOK_SIZE={{.ROOK_SIZE}} \
        MAC_ADDRESS={{.MAC_ADDRESS}} \
        BOOT_ZVOL={{.POOL}}/VM/{{.NAME}}-boot \
        OPENEBS_ZVOL={{.POOL}}/VM/{{.NAME}}-ebs \
        ROOK_ZVOL={{.POOL}}/VM/{{.NAME}}-rook \
        SKIP_ZVOL_CREATE={{.SKIP_ZVOL_CREATE}}
    vars:
      POOL: '{{.POOL | default "flashstor"}}'
      MEMORY: '{{.MEMORY | default "4096"}}'
      VCPUS: '{{.VCPUS | default "2"}}'
      DISK_SIZE: '{{.DISK_SIZE | default "250"}}'
      OPENEBS_SIZE: '{{.OPENEBS_SIZE | default "1024"}}'
      ROOK_SIZE: '{{.ROOK_SIZE | default "800"}}'
      SKIP_ZVOL_CREATE: '{{.SKIP_ZVOL_CREATE | default "false"}}'
    requires:
      vars: [NAME]

  show-zvol-pattern:
    desc: Show ZVol naming pattern for a VM [NAME=required] [POOL=flashstor]
    cmd: |
      echo "ZVol naming pattern for VM: {{.NAME}}"
      echo "Pool: {{.POOL}}"
      echo ""
      echo "Boot disk:    {{.POOL}}/VM/{{.NAME}}-boot (250GB default)"
      echo "OpenEBS disk: {{.POOL}}/VM/{{.NAME}}-ebs (1TB default)"
      echo "Rook disk:    {{.POOL}}/VM/{{.NAME}}-rook (800GB default)"
      echo ""
      echo "Device paths:"
      echo "Boot disk:    /dev/zvol/{{.POOL}}/VM/{{.NAME}}-boot"
      echo "OpenEBS disk: /dev/zvol/{{.POOL}}/VM/{{.NAME}}-ebs"
      echo "Rook disk:    /dev/zvol/{{.POOL}}/VM/{{.NAME}}-rook"
      echo ""
      echo "Task command example:"
      echo "task talos:deploy-vm-with-pattern NAME={{.NAME}} MEMORY=8192 VCPUS=4"
    vars:
      POOL: '{{.POOL | default "flashstor"}}'
    requires:
      vars: [NAME]
