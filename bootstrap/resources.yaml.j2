{% for namespace in ["external-secrets", "flux-system", "network"] %}
---
apiVersion: v1
kind: Namespace
metadata:
  name: {{ namespace }}
{% endfor %}
---
apiVersion: v1
kind: Secret
metadata:
  name: onepassword-secret
  namespace: external-secrets
stringData:
  token: op://Infrastructure/1password/OP_CONNECT_TOKEN
---
apiVersion: v1
kind: Secret
metadata:
  name: sops-age
  namespace: flux-system
stringData:
  age.agekey: op://Infrastructure/sops/SOPS_PRIVATE_KEY
---
apiVersion: v1
kind: Secret
metadata:
  name: home-ops-wc-tls
  namespace: kube-system
  annotations:
    cert-manager.io/alt-names: 'op://Infrastructure/cluster-config/SECRET_DOMAIN,op://Infrastructure/cluster-config/SECRET_WC_DOMAIN'
    cert-manager.io/certificate-name: home-ops-wc-tls
    cert-manager.io/common-name: op://Infrastructure/cluster-config/SECRET_DOMAIN
    cert-manager.io/ip-sans: ""
    cert-manager.io/issuer-group: ""
    cert-manager.io/issuer-kind: ClusterIssuer
    cert-manager.io/issuer-name: gts-production
    cert-manager.io/uri-sans: ""
  labels:
    controller.cert-manager.io/fao: "true"
type: kubernetes.io/tls
data:
  tls.crt: op://Infrastructure/home-ops-wc-tls/tls.crt
  tls.key: op://Infrastructure/home-ops-wc-tls/tls.key
---
apiVersion: v1
kind: Secret
metadata:
  name: cloudflare-tunnel-id-secret
  namespace: network
stringData:
  CLOUDFLARE_TUNNEL_ID: op://Infrastructure/cloudflare/CLOUDFLARE_TUNNEL_ID
