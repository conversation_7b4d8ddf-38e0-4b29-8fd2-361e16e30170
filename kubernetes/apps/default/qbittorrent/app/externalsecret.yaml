---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: qbittorrent
spec:
  refreshInterval: 5m
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: qbittorrent-secret
    creationPolicy: Owner
  data:
    - secretKey: WIREGUARD_ENDPOINT_IP
      remoteRef:
        key: protonvpn
        property: QBITTORRENT_VPN_ENDPOINT_IP
    - secretKey: WIREGUARD_PUBLIC_KEY
      remoteRef:
        key: protonvpn
        property: QBITTORRENT_WIREGUARD_PUBLIC_KEY
    - secretKey: WIREGUARD_PRIVATE_KEY
      remoteRef:
        key: protonvpn
        property: QBITTORRENT_WIREGUARD_PRIVATE_KEY
    - secretKey: WIREGUARD_ADDRESSES
      remoteRef:
        key: protonvpn
        property: QBITTORRENT_WIREGUARD_ADDRESSES
    - secretKey: xseed_api_key
      remoteRef:
        key: cross-seed
        property: CROSS_SEED_API_KEY
    - secretKey: GLUETUN_CONTROL_SERVER_API_KEY
      remoteRef:
        key: gluetun
        property: GLUETUN_API_KEY
    - secretKey: SERVER_CITIES
      remoteRef:
        key: protonvpn
        property: SERVER_CITIES
    - secretKey: SERVER_COUNTRIES
      remoteRef:
        key: protonvpn
        property: SERVER_COUNTRIES
    - secretKey: VPN_SERVICE_PROVIDER
      remoteRef:
        key: protonvpn
        property: VPN_SERVICE_PROVIDER
    - secretKey: HEALTH_TARGET_ADDRESS
      remoteRef:
        key: protonvpn
        property: HEALTH_TARGET_ADDRESS
    - secretKey: UPDATER_VPN_SERVICE_PROVIDERS
      remoteRef:
        key: protonvpn
        property: VPN_SERVICE_PROVIDER

---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: qbittorrent-gluetun
spec:
  refreshInterval: 5m
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: qbittorrent-gluetun
    creationPolicy: Owner
    template:
      data:
        auth.toml: |
          [[roles]]
          name = "gluetun-qb-port-sync"
          routes = [
            "GET /v1/publicip/ip",
            "GET /v1/openvpn/portforwarded"
          ]
          auth = "apikey"
          apikey = "{{ .GLUETUN_API_KEY }}"
  dataFrom:
    - extract:
        key: gluetun
