---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: tqm
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  dependsOn:
    - name: qbittorrent
      namespace: default
  values:
    controllers:
      tqm:
        type: cronjob
        cronjob: &cronJobSpec
          schedule: 0 * * * *
          backoffLimit: 0
          concurrencyPolicy: Forbid
          successfulJobsHistory: 1
          failedJobsHistory: 1
          ttlSecondsAfterFinished: 3600
        initContainers:
          retag: &container
            image:
              repository: ghcr.io/home-operations/tqm
              tag: 1.16.0@sha256:c9cda45efe561187bc0b25fe5a7048ee8d8293e0431605074b8ffa7a56a8fed9
            args:
              - retag
              - qb
            resources:
              requests:
                cpu: 10m
                memory: 64Mi
              limits:
                memory: 64Mi
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities: { drop: ["ALL"] }
        containers:
          clean:
            <<: *container
            args:
              - clean
              - qb
        pod:
          restartPolicy: Never
      orphaned:
        type: cronjob
        cronjob:
          <<: *cronJobSpec
          schedule: 0 0 * * 0
          suspend: true
        containers:
          app:
            <<: *container
            args:
              - orphan
              - qb
              - --dry-run
        pod:
          restartPolicy: Never
    defaultPodOptions:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
    persistence:
      config:
        type: emptyDir
        globalMounts:
          - path: /.config/tqm
      secret-file:
        type: secret
        name: tqm-secret
        globalMounts:
          - path: /.config/tqm/config.yaml
            subPath: config.yaml
            readOnly: true
      media:
        type: nfs
        server: nas01.${SECRET_DOMAIN}
        path: /mnt/flashstor/data
        globalMounts:
          - path: /media/torrents
            subPath: torrents
            readOnly: true
